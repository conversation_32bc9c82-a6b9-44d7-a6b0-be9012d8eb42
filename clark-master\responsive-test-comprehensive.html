<!DOCTYPE html>
<html lang="en">
  <head>
    <title>Comprehensive Responsive Test - <PERSON><PERSON><PERSON> <PERSON></title>
    <meta charset="utf-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, shrink-to-fit=no"
    />

    <link
      href="https://fonts.googleapis.com/css?family=Poppins:100,200,300,400,500,600,700,800,900"
      rel="stylesheet"
    />
    <link rel="stylesheet" href="css/open-iconic-bootstrap.min.css" />
    <link rel="stylesheet" href="css/animate.css" />
    <link rel="stylesheet" href="css/owl.carousel.min.css" />
    <link rel="stylesheet" href="css/owl.theme.default.min.css" />
    <link rel="stylesheet" href="css/magnific-popup.css" />
    <link rel="stylesheet" href="css/aos.css" />
    <link rel="stylesheet" href="css/ionicons.min.css" />
    <link rel="stylesheet" href="css/flaticon.css" />
    <link rel="stylesheet" href="css/icomoon.css" />
    <link rel="stylesheet" href="css/style.css" />

    <style>
      .debug-panel {
        position: fixed;
        top: 10px;
        right: 10px;
        background: rgba(0, 0, 0, 0.9);
        color: white;
        padding: 15px;
        border-radius: 8px;
        z-index: 9999;
        font-size: 12px;
        max-width: 280px;
        font-family: "Courier New", monospace;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
      }

      .test-controls {
        position: fixed;
        bottom: 10px;
        left: 10px;
        background: rgba(0, 0, 0, 0.9);
        color: white;
        padding: 15px;
        border-radius: 8px;
        z-index: 9999;
        font-size: 12px;
        max-width: 300px;
      }

      .test-controls button {
        margin: 3px;
        padding: 8px 12px;
        background: #007bff;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 11px;
        transition: background 0.2s;
      }

      .test-controls button:hover {
        background: #0056b3;
      }

      .test-controls button.active {
        background: #28a745;
      }

      .status-indicator {
        display: inline-block;
        width: 10px;
        height: 10px;
        border-radius: 50%;
        margin-right: 8px;
      }

      .status-good {
        background: #28a745;
      }
      .status-bad {
        background: #dc3545;
      }
      .status-warning {
        background: #ffc107;
      }

      .section-test {
        margin: 5px 0;
        padding: 5px;
        border-left: 3px solid #007bff;
        background: rgba(255, 255, 255, 0.1);
      }

      .device-frame {
        border: 3px solid #333;
        margin: 0 auto;
        transition: all 0.3s ease;
      }

      .device-frame.iphone {
        border-radius: 25px;
        border-color: #000;
      }

      .device-frame.android {
        border-radius: 15px;
        border-color: #4caf50;
      }

      .device-frame.tablet {
        border-radius: 10px;
        border-color: #ff9800;
      }
    </style>
  </head>
  <body>
    <div class="debug-panel">
      <strong>📱 Responsive Test Dashboard</strong><br />
      <hr style="margin: 10px 0; border-color: #444" />

      <div><strong>Device Info:</strong></div>
      <div>Viewport: <span id="viewport-size">Loading...</span></div>
      <div>Breakpoint: <span id="breakpoint">Loading...</span></div>
      <div>Orientation: <span id="orientation">Loading...</span></div>
      <div>Device Pixel Ratio: <span id="dpr">Loading...</span></div>

      <hr style="margin: 10px 0; border-color: #444" />

      <div class="section-test">
        <strong>🏠 Home Section:</strong><br />
        <span class="status-indicator" id="home-layout-status"></span>Layout:
        <span id="home-layout-text">Checking...</span><br />
        <span class="status-indicator" id="home-overlap-status"></span>Overlap:
        <span id="home-overlap-text">Checking...</span>
      </div>

      <div class="section-test">
        <strong>👤 About Section:</strong><br />
        <span class="status-indicator" id="about-image-status"></span>Image:
        <span id="about-image-text">Checking...</span><br />
        <span class="status-indicator" id="about-text-status"></span>Text:
        <span id="about-text-text">Checking...</span>
      </div>

      <div class="section-test">
        <strong>🛠️ Services Section:</strong><br />
        <span class="status-indicator" id="services-status"></span>Layout:
        <span id="services-text">Checking...</span>
      </div>

      <div class="section-test">
        <strong>📝 Blog Section:</strong><br />
        <span class="status-indicator" id="blog-status"></span>Cards:
        <span id="blog-text">Checking...</span>
      </div>

      <div class="section-test">
        <strong>📞 Contact Section:</strong><br />
        <span class="status-indicator" id="contact-status"></span>Form:
        <span id="contact-text">Checking...</span>
      </div>
    </div>

    <div class="test-controls">
      <div><strong>📱 Device Simulation:</strong></div>
      <button onclick="simulateDevice(320, 568, 'iPhone SE', 'iphone')">
        iPhone SE
      </button>
      <button onclick="simulateDevice(375, 667, 'iPhone 8', 'iphone')">
        iPhone 8
      </button>
      <button onclick="simulateDevice(414, 896, 'iPhone 11', 'iphone')">
        iPhone 11
      </button>
      <button onclick="simulateDevice(360, 640, 'Galaxy S10', 'android')">
        Galaxy S10
      </button>
      <button onclick="simulateDevice(768, 1024, 'iPad', 'tablet')">
        iPad
      </button>
      <button onclick="simulateDevice(1024, 768, 'iPad Landscape', 'tablet')">
        iPad Land
      </button>
      <button onclick="resetViewport()">Reset</button>

      <div style="margin-top: 10px"><strong>🔍 Section Tests:</strong></div>
      <button onclick="scrollToSection('home')">Home</button>
      <button onclick="scrollToSection('about')">About</button>
      <button onclick="scrollToSection('services')">Services</button>
      <button onclick="scrollToSection('projects')">Projects</button>
      <button onclick="scrollToSection('blog')">Blog</button>
      <button onclick="scrollToSection('contact')">Contact</button>

      <div style="margin-top: 10px"><strong>⚡ Quick Actions:</strong></div>
      <button onclick="runFullTest()">Run Full Test</button>
      <button onclick="toggleDebugMode()">Debug Mode</button>
      <button onclick="exportReport()">Export Report</button>
    </div>

    <!-- Navigation -->
    <nav
      class="navbar navbar-expand-lg navbar-dark ftco_navbar ftco-navbar-light site-navbar-target"
      id="ftco-navbar"
    >
      <div class="container">
        <a class="navbar-brand" href="index.html">Responsive Test</a>
        <button
          class="navbar-toggler js-fh5co-nav-toggle fh5co-nav-toggle"
          type="button"
          data-toggle="collapse"
          data-target="#ftco-nav"
        >
          <span class="oi oi-menu"></span> Menu
        </button>
        <div class="collapse navbar-collapse" id="ftco-nav">
          <ul class="navbar-nav nav ml-auto">
            <li class="nav-item">
              <a href="#home-section" class="nav-link"><span>Home</span></a>
            </li>
            <li class="nav-item">
              <a href="#about-section" class="nav-link"><span>About</span></a>
            </li>
            <li class="nav-item">
              <a href="#services-section" class="nav-link"
                ><span>Services</span></a
              >
            </li>
            <li class="nav-item">
              <a href="#projects-section" class="nav-link"
                ><span>Projects</span></a
              >
            </li>
            <li class="nav-item">
              <a href="#blog-section" class="nav-link"><span>Blog</span></a>
            </li>
            <li class="nav-item">
              <a href="#contact-section" class="nav-link"
                ><span>Contact</span></a
              >
            </li>
          </ul>
        </div>
      </div>
    </nav>

    <!-- Home Section -->
    <section id="home-section" class="hero">
      <div class="home-slider owl-carousel">
        <div class="slider-item">
          <div class="overlay"></div>
          <div class="container">
            <div
              class="row d-flex slider-text align-items-center"
              data-scrollax-parent="true"
            >
              <div
                class="col-md-6 col-lg-7 d-flex align-items-center ftco-animate"
              >
                <div class="text w-100">
                  <span class="subheading">Hello!</span>
                  <h1 class="mb-4 mt-3">I'm <span>Kibru Michael</span></h1>
                  <h2 class="mb-4">A Freelance Full Stack Developer</h2>
                  <p>
                    <a href="#" class="btn btn-primary py-3 px-4">Hire me</a>
                    <a
                      href="#"
                      class="btn btn-white btn-outline-white py-3 px-4"
                      >My works</a
                    >
                  </p>
                </div>
              </div>
              <div
                class="col-md-6 col-lg-5 d-flex align-items-center justify-content-center ftco-animate"
              >
                <div
                  class="home-image-container d-flex align-items-center justify-content-center"
                >
                  <div class="profile-image-wrapper">
                    <img
                      src="images/wef.gif"
                      alt="Profile"
                      class="profile-image"
                    />
                    <div class="profile-overlay"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- About Section -->
    <section class="ftco-about img ftco-section ftco-no-pb" id="about-section">
      <div class="container">
        <div class="row d-flex">
          <div class="col-md-6 col-lg-5 d-flex ftco-animate">
            <div class="img-about img d-flex align-items-stretch">
              <div class="overlay"></div>
              <div
                class="img d-flex align-self-stretch align-items-center"
                style="background-image: url(images/kebnew.png)"
              ></div>
            </div>
          </div>
          <div class="col-md-6 col-lg-7 pl-lg-5 pb-5 ftco-animate">
            <div class="row justify-content-start pb-3">
              <div class="col-md-12 heading-section ftco-animate">
                <h1 class="big">About</h1>
                <h2 class="mb-4">About Me</h2>
                <p>
                  Full-stack developer focused on clean code, modern tech, and
                  user-friendly, reliable applications.
                </p>
                <ul class="about-info mt-4 px-md-0 px-2">
                  <li class="d-flex">
                    <span>Name:</span> <span>Kibru Michael</span>
                  </li>
                  <li class="d-flex">
                    <span>Email:</span> <span><EMAIL></span>
                  </li>
                  <li class="d-flex">
                    <span>Phone:</span> <span>+************-03</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Services Section -->
    <section class="ftco-section" id="services-section">
      <div class="container">
        <div class="row justify-content-center py-5 mt-5">
          <div class="col-md-12 heading-section text-center ftco-animate">
            <h1 class="big big-2">Services</h1>
            <h2 class="mb-4">Services</h2>
          </div>
        </div>
        <div class="row">
          <div class="col-md-4 text-center d-flex ftco-animate">
            <a href="#" class="services-1">
              <span class="icon">
                <img
                  src="images/ux.png"
                  alt="UI/UX Design"
                  class="service-png-icon"
                />
              </span>
              <div class="desc">
                <h3 class="mb-5">UI/UX Design</h3>
              </div>
            </a>
          </div>
          <div class="col-md-4 text-center d-flex ftco-animate">
            <a href="#" class="services-1">
              <span class="icon">
                <img
                  src="images/full.png"
                  alt="Full-Stack Development"
                  class="service-png-icon"
                />
              </span>
              <div class="desc">
                <h3 class="mb-5">Full-Stack Development</h3>
              </div>
            </a>
          </div>
          <div class="col-md-4 text-center d-flex ftco-animate">
            <a href="#" class="services-1">
              <span class="icon">
                <img
                  src="images/data-v.png"
                  alt="Data Visualization"
                  class="service-png-icon"
                />
              </span>
              <div class="desc">
                <h3 class="mb-5">Data Visualization</h3>
              </div>
            </a>
          </div>
        </div>
      </div>
    </section>

    <!-- Projects Section -->
    <section class="ftco-section ftco-project" id="projects-section">
      <div class="container">
        <div class="row justify-content-center pb-5">
          <div class="col-md-12 heading-section text-center ftco-animate">
            <h1 class="big big-2">Projects</h1>
            <h2 class="mb-4">Our Projects</h2>
          </div>
        </div>
        <div class="row">
          <div class="col-md-4">
            <div
              class="project img ftco-animate d-flex justify-content-center align-items-center"
              style="background-image: url(images/project-4.jpg)"
            >
              <div class="overlay"></div>
              <div class="text text-center p-4">
                <h3><a href="#">Branding &amp; Illustration Design</a></h3>
                <span>Web Design</span>
              </div>
            </div>
          </div>
          <div class="col-md-4">
            <div
              class="project img ftco-animate d-flex justify-content-center align-items-center"
              style="background-image: url(images/project-5.jpg)"
            >
              <div class="overlay"></div>
              <div class="text text-center p-4">
                <h3><a href="#">Mobile App Development</a></h3>
                <span>Mobile Development</span>
              </div>
            </div>
          </div>
          <div class="col-md-4">
            <div
              class="project img ftco-animate d-flex justify-content-center align-items-center"
              style="background-image: url(images/project-6.jpg)"
            >
              <div class="overlay"></div>
              <div class="text text-center p-4">
                <h3><a href="#">E-commerce Platform</a></h3>
                <span>Web Development</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Blog Section -->
    <section class="ftco-section" id="blog-section">
      <div class="container">
        <div class="row justify-content-center mb-5 pb-5">
          <div class="col-md-7 heading-section text-center ftco-animate">
            <h1 class="big big-2">Blog</h1>
            <h2 class="mb-4">Our Blog</h2>
          </div>
        </div>
        <div class="row d-flex">
          <div class="col-md-4 d-flex ftco-animate">
            <div class="blog-entry justify-content-end">
              <a
                href="#"
                class="block-20"
                style="background-image: url('images/image1.jpg')"
              ></a>
              <div class="text mt-3 float-right d-block">
                <div class="d-flex align-items-center mb-3 meta">
                  <p class="mb-0">
                    <span class="mr-2">June 21, 2019</span>
                    <a href="#" class="mr-2">Admin</a>
                    <a href="#" class="meta-chat"
                      ><span class="icon-chat"></span> 3</a
                    >
                  </p>
                </div>
                <h3 class="heading">
                  <a href="#">Why Lead Generation is Key for Business Growth</a>
                </h3>
                <p>
                  A small river named Duden flows by their place and supplies it
                  with the necessary regelialia.
                </p>
              </div>
            </div>
          </div>
          <div class="col-md-4 d-flex ftco-animate">
            <div class="blog-entry justify-content-end">
              <a
                href="#"
                class="block-20"
                style="background-image: url('images/image2.jpg')"
              ></a>
              <div class="text mt-3 float-right d-block">
                <div class="d-flex align-items-center mb-3 meta">
                  <p class="mb-0">
                    <span class="mr-2">June 21, 2019</span>
                    <a href="#" class="mr-2">Admin</a>
                    <a href="#" class="meta-chat"
                      ><span class="icon-chat"></span> 3</a
                    >
                  </p>
                </div>
                <h3 class="heading">
                  <a href="#">Why Lead Generation is Key for Business Growth</a>
                </h3>
                <p>
                  A small river named Duden flows by their place and supplies it
                  with the necessary regelialia.
                </p>
              </div>
            </div>
          </div>
          <div class="col-md-4 d-flex ftco-animate">
            <div class="blog-entry justify-content-end">
              <a
                href="#"
                class="block-20"
                style="background-image: url('images/image3.jpg')"
              ></a>
              <div class="text mt-3 float-right d-block">
                <div class="d-flex align-items-center mb-3 meta">
                  <p class="mb-0">
                    <span class="mr-2">June 21, 2019</span>
                    <a href="#" class="mr-2">Admin</a>
                    <a href="#" class="meta-chat"
                      ><span class="icon-chat"></span> 3</a
                    >
                  </p>
                </div>
                <h3 class="heading">
                  <a href="#">Why Lead Generation is Key for Business Growth</a>
                </h3>
                <p>
                  A small river named Duden flows by their place and supplies it
                  with the necessary regelialia.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Contact Section -->
    <section
      class="ftco-section contact-section ftco-no-pb"
      id="contact-section"
    >
      <div class="container">
        <div class="row justify-content-center mb-5 pb-3">
          <div class="col-md-7 heading-section text-center ftco-animate">
            <h1 class="big big-2">Contact</h1>
            <h2 class="mb-4">Contact Me</h2>
          </div>
        </div>
        <div class="row d-flex contact-info mb-5">
          <div class="col-md-6 col-lg-3 d-flex ftco-animate">
            <div class="align-self-stretch box p-4 text-center">
              <div
                class="icon d-flex align-items-center justify-content-center"
              >
                <span class="icon-map-signs"></span>
              </div>
              <h3 class="mb-4">Address</h3>
              <p>Addis Ababa, Ethiopia</p>
            </div>
          </div>
          <div class="col-md-6 col-lg-3 d-flex ftco-animate">
            <div class="align-self-stretch box p-4 text-center">
              <div
                class="icon d-flex align-items-center justify-content-center"
              >
                <span class="icon-phone2"></span>
              </div>
              <h3 class="mb-4">Contact Number</h3>
              <p><a href="tel://+251911571403">+************-03</a></p>
            </div>
          </div>
          <div class="col-md-6 col-lg-3 d-flex ftco-animate">
            <div class="align-self-stretch box p-4 text-center">
              <div
                class="icon d-flex align-items-center justify-content-center"
              >
                <span class="icon-paper-plane"></span>
              </div>
              <h3 class="mb-4">Email Address</h3>
              <p><a href="mailto:<EMAIL>"><EMAIL></a></p>
            </div>
          </div>
          <div class="col-md-6 col-lg-3 d-flex ftco-animate">
            <div class="align-self-stretch box p-4 text-center">
              <div
                class="icon d-flex align-items-center justify-content-center"
              >
                <span class="icon-globe"></span>
              </div>
              <h3 class="mb-4">Website</h3>
              <p><a href="#">yoursite.com</a></p>
            </div>
          </div>
        </div>
        <div class="row no-gutters block-9">
          <div class="col-md-6 order-md-last d-flex">
            <form action="#" class="bg-light p-4 p-md-5 contact-form">
              <div class="form-group">
                <input
                  type="text"
                  class="form-control"
                  placeholder="Your Name"
                />
              </div>
              <div class="form-group">
                <input
                  type="text"
                  class="form-control"
                  placeholder="Your Email"
                />
              </div>
              <div class="form-group">
                <input type="text" class="form-control" placeholder="Subject" />
              </div>
              <div class="form-group">
                <textarea
                  name=""
                  id=""
                  cols="30"
                  rows="7"
                  class="form-control"
                  placeholder="Message"
                ></textarea>
              </div>
              <div class="form-group">
                <input
                  type="submit"
                  value="Send Message"
                  class="btn btn-primary py-3 px-5"
                />
              </div>
            </form>
          </div>
          <div class="col-md-6 d-flex">
            <div
              class="img"
              style="background-image: url(images/about.gif)"
            ></div>
          </div>
        </div>
      </div>
    </section>

    <script src="js/jquery.min.js"></script>
    <script src="js/jquery-migrate-3.0.1.min.js"></script>
    <script src="js/popper.min.js"></script>
    <script src="js/bootstrap.min.js"></script>
    <script src="js/jquery.easing.1.3.js"></script>
    <script src="js/jquery.waypoints.min.js"></script>
    <script src="js/jquery.stellar.min.js"></script>
    <script src="js/owl.carousel.min.js"></script>
    <script src="js/jquery.magnific-popup.min.js"></script>
    <script src="js/aos.js"></script>
    <script src="js/jquery.animateNumber.min.js"></script>
    <script src="js/scrollax.min.js"></script>
    <script src="js/main.js"></script>

    <script>
      let testResults = {};
      let debugMode = false;

      function updateDebugInfo() {
        const width = window.innerWidth;
        const height = window.innerHeight;

        document.getElementById(
          "viewport-size"
        ).textContent = `${width}x${height}`;
        document.getElementById("orientation").textContent =
          width > height ? "Landscape" : "Portrait";
        document.getElementById("dpr").textContent =
          window.devicePixelRatio || 1;

        // Determine breakpoint
        let breakpoint = "";
        if (width < 576) breakpoint = "XS (< 576px)";
        else if (width < 768) breakpoint = "SM (576px - 767px)";
        else if (width < 992) breakpoint = "MD (768px - 991px)";
        else if (width < 1200) breakpoint = "LG (992px - 1199px)";
        else breakpoint = "XL (≥ 1200px)";

        document.getElementById("breakpoint").textContent = breakpoint;

        // Run all section tests
        checkHomeSection();
        checkAboutSection();
        checkServicesSection();
        checkBlogSection();
        checkContactSection();
      }

      function checkHomeSection() {
        const textElement = document.querySelector(".col-md-6.col-lg-7");
        const imageElement = document.querySelector(".col-md-6.col-lg-5");

        if (!textElement || !imageElement) {
          updateStatus("home-layout", "bad", "Elements not found");
          updateStatus("home-overlap", "bad", "Elements not found");
          return;
        }

        const textRect = textElement.getBoundingClientRect();
        const imageRect = imageElement.getBoundingClientRect();

        // Check for overlap
        const isOverlapping = !(
          textRect.right < imageRect.left ||
          imageRect.right < textRect.left ||
          textRect.bottom < imageRect.top ||
          imageRect.bottom < textRect.top
        );

        // Check layout based on screen size
        const width = window.innerWidth;
        let layoutGood = false;
        let layoutText = "";

        if (width <= 767) {
          // Mobile: should be stacked vertically
          const isStacked =
            Math.abs(textRect.top - imageRect.bottom) < 100 ||
            Math.abs(imageRect.top - textRect.bottom) < 100;
          layoutGood = isStacked;
          layoutText = isStacked ? "Stacked ✓" : "Not Stacked ✗";
        } else {
          // Desktop: should be side-by-side
          const isSideBySide = Math.abs(textRect.top - imageRect.top) < 100;
          layoutGood = isSideBySide;
          layoutText = isSideBySide ? "Side-by-side ✓" : "Not Side-by-side ✗";
        }

        updateStatus("home-layout", layoutGood ? "good" : "bad", layoutText);
        updateStatus(
          "home-overlap",
          isOverlapping ? "bad" : "good",
          isOverlapping ? "Detected ✗" : "None ✓"
        );

        testResults.home = { layout: layoutGood, overlap: !isOverlapping };
      }

      function checkAboutSection() {
        const aboutImage = document.querySelector(
          ".ftco-about .img-about .img"
        );
        const aboutText = document.querySelector(
          ".ftco-about .col-md-6.col-lg-7"
        );

        if (!aboutImage || !aboutText) {
          updateStatus("about-image", "bad", "Elements not found");
          updateStatus("about-text", "bad", "Elements not found");
          return;
        }

        const imageRect = aboutImage.getBoundingClientRect();
        const textRect = aboutText.getBoundingClientRect();

        const imageVisible = imageRect.width > 0 && imageRect.height > 100;
        const textVisible = textRect.width > 0 && textRect.height > 0;

        updateStatus(
          "about-image",
          imageVisible ? "good" : "bad",
          imageVisible
            ? `Visible (${Math.round(imageRect.height)}px) ✓`
            : "Hidden ✗"
        );
        updateStatus(
          "about-text",
          textVisible ? "good" : "bad",
          textVisible ? "Readable ✓" : "Hidden ✗"
        );

        testResults.about = { image: imageVisible, text: textVisible };
      }

      function checkServicesSection() {
        const serviceCards = document.querySelectorAll(".services-1");
        const servicesContainer = document.querySelector(
          "#services-section .row"
        );

        if (!serviceCards.length || !servicesContainer) {
          updateStatus("services", "bad", "Elements not found");
          return;
        }

        let allCardsVisible = true;
        let properSpacing = true;

        serviceCards.forEach((card, index) => {
          const rect = card.getBoundingClientRect();
          if (rect.width === 0 || rect.height === 0) {
            allCardsVisible = false;
          }
        });

        const width = window.innerWidth;
        if (width <= 767) {
          // Mobile: cards should stack vertically
          let lastBottom = 0;
          serviceCards.forEach((card) => {
            const rect = card.getBoundingClientRect();
            if (lastBottom > 0 && rect.top < lastBottom - 50) {
              properSpacing = false;
            }
            lastBottom = rect.bottom;
          });
        }

        const servicesGood = allCardsVisible && properSpacing;
        updateStatus(
          "services",
          servicesGood ? "good" : "bad",
          servicesGood ? "Cards aligned ✓" : "Layout issues ✗"
        );

        testResults.services = {
          visible: allCardsVisible,
          spacing: properSpacing,
        };
      }

      function checkBlogSection() {
        const blogCards = document.querySelectorAll(".blog-entry");

        if (!blogCards.length) {
          updateStatus("blog", "bad", "Elements not found");
          return;
        }

        let allCardsVisible = true;
        let imagesLoaded = true;

        blogCards.forEach((card) => {
          const rect = card.getBoundingClientRect();
          const image = card.querySelector(".block-20");

          if (rect.width === 0 || rect.height === 0) {
            allCardsVisible = false;
          }

          if (image) {
            const imageRect = image.getBoundingClientRect();
            if (imageRect.height < 100) {
              imagesLoaded = false;
            }
          }
        });

        const blogGood = allCardsVisible && imagesLoaded;
        updateStatus(
          "blog",
          blogGood ? "good" : "bad",
          blogGood ? "Cards responsive ✓" : "Layout issues ✗"
        );

        testResults.blog = { visible: allCardsVisible, images: imagesLoaded };
      }

      function checkContactSection() {
        const contactForm = document.querySelector(".contact-form");
        const contactBoxes = document.querySelectorAll(".contact-section .box");

        if (!contactForm || !contactBoxes.length) {
          updateStatus("contact", "bad", "Elements not found");
          return;
        }

        const formRect = contactForm.getBoundingClientRect();
        const formVisible = formRect.width > 0 && formRect.height > 0;

        let boxesVisible = true;
        contactBoxes.forEach((box) => {
          const rect = box.getBoundingClientRect();
          if (rect.width === 0 || rect.height === 0) {
            boxesVisible = false;
          }
        });

        const contactGood = formVisible && boxesVisible;
        updateStatus(
          "contact",
          contactGood ? "good" : "bad",
          contactGood ? "Form responsive ✓" : "Layout issues ✗"
        );

        testResults.contact = { form: formVisible, boxes: boxesVisible };
      }

      function updateStatus(elementId, status, text) {
        const statusElement = document.getElementById(`${elementId}-status`);
        const textElement = document.getElementById(`${elementId}-text`);

        if (statusElement) {
          statusElement.className = `status-indicator status-${status}`;
        }
        if (textElement) {
          textElement.textContent = text;
        }
      }

      function simulateDevice(width, height, name, type = "") {
        document.body.style.width = width + "px";
        document.body.style.maxWidth = width + "px";
        document.body.style.height = height + "px";
        document.body.style.overflow = "auto";
        document.body.style.margin = "0 auto";
        document.body.className = `device-frame ${type}`;

        // Update debug info after simulation
        setTimeout(() => {
          updateDebugInfo();
          console.log(`📱 Simulating ${name} (${width}x${height})`);
        }, 100);
      }

      function resetViewport() {
        document.body.style.width = "";
        document.body.style.maxWidth = "";
        document.body.style.height = "";
        document.body.style.overflow = "";
        document.body.style.margin = "";
        document.body.className = "";
        updateDebugInfo();
        console.log("🔄 Viewport reset to normal");
      }

      function scrollToSection(section) {
        const element = document.getElementById(section + "-section");
        if (element) {
          element.scrollIntoView({ behavior: "smooth" });
          setTimeout(updateDebugInfo, 500);
        }
      }

      function runFullTest() {
        console.log("🧪 Running full responsive test...");

        const devices = [
          { width: 320, height: 568, name: "iPhone SE" },
          { width: 375, height: 667, name: "iPhone 8" },
          { width: 414, height: 896, name: "iPhone 11" },
          { width: 768, height: 1024, name: "iPad" },
          { width: 1024, height: 768, name: "iPad Landscape" },
          { width: 1200, height: 800, name: "Desktop" },
        ];

        let currentDevice = 0;

        function testNextDevice() {
          if (currentDevice < devices.length) {
            const device = devices[currentDevice];
            simulateDevice(device.width, device.height, device.name);

            setTimeout(() => {
              console.log(
                `✅ Tested ${device.name}: ${JSON.stringify(testResults)}`
              );
              currentDevice++;
              testNextDevice();
            }, 2000);
          } else {
            resetViewport();
            console.log("🎉 Full test completed!");
            alert(
              "Full responsive test completed! Check console for detailed results."
            );
          }
        }

        testNextDevice();
      }

      function toggleDebugMode() {
        debugMode = !debugMode;
        if (debugMode) {
          document.body.style.outline = "2px solid red";
          document
            .querySelectorAll(".col-md-6, .col-lg-5, .col-lg-7")
            .forEach((el) => {
              el.style.outline = "1px solid blue";
            });
          console.log("🐛 Debug mode enabled");
        } else {
          document.body.style.outline = "";
          document
            .querySelectorAll(".col-md-6, .col-lg-5, .col-lg-7")
            .forEach((el) => {
              el.style.outline = "";
            });
          console.log("🐛 Debug mode disabled");
        }
      }

      function exportReport() {
        const report = {
          timestamp: new Date().toISOString(),
          viewport: {
            width: window.innerWidth,
            height: window.innerHeight,
            devicePixelRatio: window.devicePixelRatio,
          },
          userAgent: navigator.userAgent,
          testResults: testResults,
        };

        const blob = new Blob([JSON.stringify(report, null, 2)], {
          type: "application/json",
        });
        const url = URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = `responsive-test-report-${Date.now()}.json`;
        a.click();
        URL.revokeObjectURL(url);

        console.log("📊 Test report exported:", report);
      }

      // Initialize
      updateDebugInfo();
      window.addEventListener("resize", updateDebugInfo);
      setInterval(updateDebugInfo, 3000); // Check every 3 seconds

      // Initialize owl carousel
      $(".home-slider").owlCarousel({
        loop: true,
        autoplay: false,
        margin: 0,
        animateOut: "fadeOut",
        animateIn: "fadeIn",
        nav: true,
        autoplayHoverPause: false,
        items: 1,
        navText: [
          "<span class='ion-md-arrow-back'></span>",
          "<span class='ion-chevron-right'></span>",
        ],
        responsive: {
          0: { items: 1 },
          600: { items: 1 },
          1000: { items: 1 },
        },
      });

      console.log("🚀 Comprehensive Responsive Test initialized!");
      console.log("📱 Use the control panel to test different devices");
      console.log(
        '🔍 Click "Run Full Test" for automated testing across all devices'
      );
    </script>
  </body>
</html>
