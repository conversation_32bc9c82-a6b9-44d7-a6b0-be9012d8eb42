<!DOCTYPE html>
<html lang="en">
<head>
    <title>Mobile Fix Test - Home & About Sections</title>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
    
    <link href="https://fonts.googleapis.com/css?family=Poppins:100,200,300,400,500,600,700,800,900" rel="stylesheet" />
    <link rel="stylesheet" href="css/open-iconic-bootstrap.min.css" />
    <link rel="stylesheet" href="css/animate.css" />
    <link rel="stylesheet" href="css/owl.carousel.min.css" />
    <link rel="stylesheet" href="css/owl.theme.default.min.css" />
    <link rel="stylesheet" href="css/magnific-popup.css" />
    <link rel="stylesheet" href="css/aos.css" />
    <link rel="stylesheet" href="css/ionicons.min.css" />
    <link rel="stylesheet" href="css/flaticon.css" />
    <link rel="stylesheet" href="css/icomoon.css" />
    <link rel="stylesheet" href="css/style.css" />
    
    <style>
        .debug-info {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0,0,0,0.9);
            color: white;
            padding: 15px;
            border-radius: 5px;
            z-index: 9999;
            font-size: 12px;
            max-width: 250px;
            font-family: monospace;
        }
        .test-controls {
            position: fixed;
            bottom: 10px;
            left: 10px;
            background: rgba(0,0,0,0.9);
            color: white;
            padding: 15px;
            border-radius: 5px;
            z-index: 9999;
            font-size: 12px;
        }
        .test-controls button {
            margin: 2px;
            padding: 8px 12px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            font-size: 11px;
        }
        .test-controls button:hover {
            background: #0056b3;
        }
        .test-controls button.active {
            background: #28a745;
        }
        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 5px;
        }
        .status-good { background: #28a745; }
        .status-bad { background: #dc3545; }
        .status-warning { background: #ffc107; }
    </style>
</head>
<body>
    <div class="debug-info">
        <strong>Mobile Fix Test</strong><br>
        Viewport: <span id="viewport-size"></span><br>
        Breakpoint: <span id="breakpoint"></span><br>
        <hr style="margin: 10px 0;">
        <strong>Home Section:</strong><br>
        <span class="status-indicator" id="home-status"></span>Layout: <span id="home-layout">Checking...</span><br>
        <span class="status-indicator" id="overlap-status"></span>Overlap: <span id="overlap-text">Checking...</span><br>
        <hr style="margin: 10px 0;">
        <strong>About Section:</strong><br>
        <span class="status-indicator" id="about-status"></span>Image: <span id="about-image">Checking...</span><br>
        <span class="status-indicator" id="height-status"></span>Height: <span id="height-text">Checking...</span>
    </div>
    
    <div class="test-controls">
        <div><strong>Device Tests:</strong></div>
        <button onclick="simulateDevice(320, 568, 'iPhone SE')">iPhone SE</button>
        <button onclick="simulateDevice(375, 667, 'iPhone 8')">iPhone 8</button>
        <button onclick="simulateDevice(414, 896, 'iPhone 11')">iPhone 11</button>
        <button onclick="simulateDevice(768, 1024, 'iPad')">iPad</button>
        <button onclick="resetViewport()">Reset</button>
        <br><br>
        <button onclick="scrollToSection('home')">Test Home</button>
        <button onclick="scrollToSection('about')">Test About</button>
    </div>

    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark ftco_navbar ftco-navbar-light site-navbar-target" id="ftco-navbar">
        <div class="container">
            <a class="navbar-brand" href="index.html">Mobile Test</a>
        </div>
    </nav>

    <!-- Home Section -->
    <section id="home-section" class="hero">
        <div class="home-slider owl-carousel">
            <div class="slider-item">
                <div class="overlay"></div>
                <div class="container">
                    <div class="row d-md-flex no-gutters slider-text align-items-center justify-content-center" data-scrollax-parent="true">
                        <div class="one-forth d-flex align-items-center ftco-animate" data-scrollax=" properties: { translateY: '70%' }">
                            <div class="text">
                                <span class="subheading">Hello!</span>
                                <h1 class="mb-4 mt-3">I'm <span>Kibru Michael</span></h1>
                                <h2 class="mb-4">A Freelance Full Stack Developer</h2>
                                <p>
                                    <a href="#" class="btn btn-primary py-3 px-4">Hire me</a>
                                    <a href="#" class="btn btn-white btn-outline-white py-3 px-4">My works</a>
                                </p>
                            </div>
                        </div>
                        <div class="one-third js-fullheight order-md-last img d-flex align-items-center justify-content-center profile-image-container">
                            <div class="profile-image-wrapper">
                                <img src="images/wef.gif" alt="Profile" class="profile-image" />
                                <div class="profile-overlay"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="slider-item">
                <div class="overlay"></div>
                <div class="container">
                    <div class="row d-md-flex no-gutters slider-text align-items-center justify-content-center" data-scrollax-parent="true">
                        <div class="one-forth d-flex align-items-center ftco-animate" data-scrollax=" properties: { translateY: '70%' }">
                            <div class="text">
                                <span class="subheading">Hello!</span>
                                <h1 class="mb-4 mt-3">I'm a <span>Full Stack Developer</span> based in Addis Ababa</h1>
                                <p>
                                    <a href="#" class="btn btn-primary py-3 px-4">Hire me</a>
                                    <a href="#" class="btn btn-white btn-outline-white py-3 px-4">My works</a>
                                </p>
                            </div>
                        </div>
                        <div class="one-third js-fullheight order-md-last img d-flex align-items-center justify-content-center profile-image-container">
                            <div class="profile-image-wrapper">
                                <img src="images/log.png" alt="Profile" class="profile-image" />
                                <div class="profile-overlay"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- About Section -->
    <section class="ftco-about img ftco-section ftco-no-pb" id="about-section">
        <div class="container">
            <div class="row d-flex">
                <!-- Image Column -->
                <div class="col-md-6 col-lg-5 d-flex ftco-animate">
                    <div class="img-about img d-flex align-items-stretch">
                        <div class="overlay"></div>
                        <div class="img d-flex align-self-stretch align-items-center" style="background-image:url(images/kebnew.png);">
                        </div>
                    </div>
                </div>

                <!-- Text Column -->
                <div class="col-md-6 col-lg-7 pl-lg-5 pb-5 ftco-animate">
                    <div class="row justify-content-start pb-3">
                        <div class="col-md-12 heading-section ftco-animate">
                            <h1 class="big">About</h1>
                            <h2 class="mb-4">About Me</h2>
                            <p>Full-stack developer focused on clean code, modern tech, and user-friendly, reliable applications.</p>
                            <ul class="about-info mt-4 px-md-0 px-2">
                                <li class="d-flex"><span>Name:</span> <span>Kibru Michael</span></li>
                                <li class="d-flex"><span>POBox:</span> <span>13908</span></li>
                                <li class="d-flex"><span>Email:</span> <span><EMAIL></span></li>
                                <li class="d-flex"><span>Phone:</span> <span>+************-03</span></li>
                            </ul>
                        </div>
                    </div>
                    <div class="counter-wrap ftco-animate d-flex mt-md-3">
                        <div class="text">
                            <p class="mb-4">
                                <span class="number" data-number="37">0</span>
                                <span>Project complete</span>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <script src="js/jquery.min.js"></script>
    <script src="js/jquery-migrate-3.0.1.min.js"></script>
    <script src="js/popper.min.js"></script>
    <script src="js/bootstrap.min.js"></script>
    <script src="js/jquery.easing.1.3.js"></script>
    <script src="js/jquery.waypoints.min.js"></script>
    <script src="js/jquery.stellar.min.js"></script>
    <script src="js/owl.carousel.min.js"></script>
    <script src="js/jquery.magnific-popup.min.js"></script>
    <script src="js/aos.js"></script>
    <script src="js/jquery.animateNumber.min.js"></script>
    <script src="js/scrollax.min.js"></script>
    <script src="js/main.js"></script>
    
    <script>
        function updateDebugInfo() {
            const width = window.innerWidth;
            document.getElementById('viewport-size').textContent = width + 'x' + window.innerHeight;
            
            // Determine breakpoint
            let breakpoint = '';
            if (width < 576) breakpoint = 'XS (< 576px)';
            else if (width < 768) breakpoint = 'SM (576px - 767px)';
            else if (width < 992) breakpoint = 'MD (768px - 991px)';
            else if (width < 1200) breakpoint = 'LG (992px - 1199px)';
            else breakpoint = 'XL (≥ 1200px)';
            
            document.getElementById('breakpoint').textContent = breakpoint;
            
            // Check layout issues
            checkHomeSection();
            checkAboutSection();
        }
        
        function checkHomeSection() {
            const textElement = document.querySelector('.one-forth');
            const imageElement = document.querySelector('.one-third');
            
            if (!textElement || !imageElement) return;
            
            const textRect = textElement.getBoundingClientRect();
            const imageRect = imageElement.getBoundingClientRect();
            
            // Check for overlap
            const isOverlapping = !(textRect.right < imageRect.left || 
                                   imageRect.right < textRect.left || 
                                   textRect.bottom < imageRect.top || 
                                   imageRect.bottom < textRect.top);
            
            // Update status
            const homeStatus = document.getElementById('home-status');
            const overlapStatus = document.getElementById('overlap-status');
            const homeLayout = document.getElementById('home-layout');
            const overlapText = document.getElementById('overlap-text');
            
            if (window.innerWidth <= 767) {
                // Mobile layout should be stacked
                const isStacked = textRect.bottom <= imageRect.top + 50; // 50px tolerance
                homeStatus.className = 'status-indicator ' + (isStacked ? 'status-good' : 'status-bad');
                homeLayout.textContent = isStacked ? 'Stacked ✓' : 'Not Stacked ✗';
            } else {
                // Desktop layout should be side-by-side
                const isSideBySide = Math.abs(textRect.top - imageRect.top) < 100;
                homeStatus.className = 'status-indicator ' + (isSideBySide ? 'status-good' : 'status-bad');
                homeLayout.textContent = isSideBySide ? 'Side-by-side ✓' : 'Not Side-by-side ✗';
            }
            
            overlapStatus.className = 'status-indicator ' + (isOverlapping ? 'status-bad' : 'status-good');
            overlapText.textContent = isOverlapping ? 'Detected ✗' : 'None ✓';
        }
        
        function checkAboutSection() {
            const aboutImage = document.querySelector('.ftco-about .img-about .img');
            
            if (!aboutImage) return;
            
            const imageRect = aboutImage.getBoundingClientRect();
            const hasHeight = imageRect.height > 100;
            const isVisible = imageRect.width > 0 && imageRect.height > 0;
            
            const aboutStatus = document.getElementById('about-status');
            const heightStatus = document.getElementById('height-status');
            const aboutImageText = document.getElementById('about-image');
            const heightText = document.getElementById('height-text');
            
            aboutStatus.className = 'status-indicator ' + (isVisible ? 'status-good' : 'status-bad');
            aboutImageText.textContent = isVisible ? 'Visible ✓' : 'Hidden ✗';
            
            heightStatus.className = 'status-indicator ' + (hasHeight ? 'status-good' : 'status-bad');
            heightText.textContent = hasHeight ? `${Math.round(imageRect.height)}px ✓` : 'Too small ✗';
        }
        
        function simulateDevice(width, height, name) {
            document.body.style.width = width + 'px';
            document.body.style.maxWidth = width + 'px';
            document.body.style.overflow = 'auto';
            document.body.style.border = '3px solid red';
            document.body.style.margin = '0 auto';
            
            // Update debug info
            setTimeout(() => {
                updateDebugInfo();
                console.log(`Simulating ${name} (${width}x${height})`);
            }, 100);
        }
        
        function resetViewport() {
            document.body.style.width = '';
            document.body.style.maxWidth = '';
            document.body.style.overflow = '';
            document.body.style.border = '';
            document.body.style.margin = '';
            updateDebugInfo();
        }
        
        function scrollToSection(section) {
            const element = document.getElementById(section + '-section');
            if (element) {
                element.scrollIntoView({ behavior: 'smooth' });
                setTimeout(updateDebugInfo, 500);
            }
        }
        
        // Initialize
        updateDebugInfo();
        window.addEventListener('resize', updateDebugInfo);
        setInterval(updateDebugInfo, 2000); // Check every 2 seconds
        
        // Initialize owl carousel
        $('.home-slider').owlCarousel({
            loop: true,
            autoplay: false, // Disabled for testing
            margin: 0,
            animateOut: 'fadeOut',
            animateIn: 'fadeIn',
            nav: true,
            autoplayHoverPause: false,
            items: 1,
            navText: ["<span class='ion-md-arrow-back'></span>", "<span class='ion-chevron-right'></span>"],
            responsive: {
                0: { items: 1 },
                600: { items: 1 },
                1000: { items: 1 }
            }
        });
    </script>
</body>
</html>
